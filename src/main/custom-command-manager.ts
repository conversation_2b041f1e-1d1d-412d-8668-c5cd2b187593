import fs from 'fs';
import path from 'path';

import chokidar from 'chokidar';
import { loadFront } from 'yaml-front-matter';

import Logger from './logger';

import type { CustomCommand } from '@common/types';

export class CustomCommandManager {
  private commandsDir: string;
  private commands: Map<string, CustomCommand> = new Map();
  private watcher: chokidar.FSWatcher | null = null;
  private logger: typeof Logger;

  constructor(projectRoot: string, logger: typeof Logger) {
    this.commandsDir = path.join(projectRoot, '.aider-desk', 'commands');
    this.logger = logger;
    this.loadAllCommands();
    this.watchCommandsDir();
  }

  private loadAllCommands() {
    if (!fs.existsSync(this.commandsDir)) {
      return;
    }
    const files = fs.readdirSync(this.commandsDir).filter((f) => f.endsWith('.md'));
    for (const file of files) {
      this.loadCommandFile(path.join(this.commandsDir, file));
    }
  }

  private loadCommandFile(filePath: string) {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const parsed = loadFront(content);
      if (!parsed.description) {
        throw new Error('Missing description in frontmatter');
      }
      const name = path.basename(filePath, '.md');
      const args = Array.isArray(parsed.arguments) ? parsed.arguments : [];
      const template = parsed.__content?.trim() || '';
      this.commands.set(name, { name, description: parsed.description, arguments: args, template });
    } catch (err) {
      this.logger.error(`[CustomCommandManager] Failed to parse ${filePath}: ${err}`);
      // Optionally: send error to chat window via IPC or callback
    }
  }

  private removeCommandFile(filePath: string) {
    const name = path.basename(filePath, '.md');
    this.commands.delete(name);
  }

  private watchCommandsDir() {
    if (!fs.existsSync(this.commandsDir)) {
      return;
    }
    this.watcher = chokidar.watch(this.commandsDir, { ignoreInitial: true });
    this.watcher
      .on('add', (file) => this.loadCommandFile(file))
      .on('change', (file) => this.loadCommandFile(file))
      .on('unlink', (file) => this.removeCommandFile(file));
  }

  getCommand(name: string): CustomCommand | undefined {
    return this.commands.get(name);
  }

  getAllCommands(): CustomCommand[] {
    return Array.from(this.commands.values());
  }
}
